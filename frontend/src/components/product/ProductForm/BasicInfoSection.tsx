/**
 * قسم البيانات الأساسية للمنتج
 * يحتوي على اسم المنتج، الرابط، SKU، التصنيف، العلامة التجارية، الوحدة، الباركود، والوصف
 * (المستودع منفصل في نظام إدارة المستودعات)
 */

import React, { useState, useEffect } from 'react';
import { FaSync } from 'react-icons/fa';
import { FiCode } from 'react-icons/fi';

// Import components
import { TextInput, ModalSelectInput } from '../../inputs';
import ArabicRichTextEditorWithButtons from '../../inputs/ArabicRichTextEditorWithButtons';

// Import stores
import useProductStore from '../../../stores/productStore';
import useBrandStore from '../../../stores/brandStore';
import useUnitStore from '../../../stores/unitStore';

interface BasicInfoSectionProps {
  formData: any;
  updateFormData: (field: string, value: any) => void;
  errors: Record<string, string>;
  generateSKU: () => string;
  generateBarcode: () => string;
}

const BasicInfoSection: React.FC<BasicInfoSectionProps> = ({
  formData,
  updateFormData,
  errors,
  generateSKU,
  generateBarcode
}) => {
  // Store hooks
  const { catalogCategories, catalogSubcategories, fetchCatalogCategories, fetchCatalogSubcategories } = useProductStore();
  const { brands, loading: brandsLoading, fetchBrands } = useBrandStore();
  const { units, loading: unitsLoading, fetchUnits } = useUnitStore();

  // Local state
  const [loadingData, setLoadingData] = useState(false);

  // Initialize data
  useEffect(() => {
    const initializeData = async () => {
      setLoadingData(true);
      try {
        await Promise.all([
          fetchCatalogCategories(),
          fetchBrands(),
          fetchUnits()
        ]);
      } catch (error) {
        console.error('خطأ في تحميل البيانات:', error);
      } finally {
        setLoadingData(false);
      }
    };

    initializeData();
  }, []);

  // Load subcategories when category changes
  useEffect(() => {
    if (formData.category_id) {
      fetchCatalogSubcategories(formData.category_id);
    }
  }, [formData.category_id]);

  // Prepare options
  const categoryOptions = catalogCategories.map((category: any) => ({
    value: category.id.toString(),
    label: category.name,
    disabled: !category.is_active
  }));

  const subcategoryOptions = catalogSubcategories
    .filter((subcategory: any) => subcategory.category_id === parseInt(formData.category_id))
    .map((subcategory: any) => ({
      value: subcategory.id.toString(),
      label: subcategory.name,
      disabled: !subcategory.is_active
    }));

  const brandOptions = brands.map(brand => ({
    value: brand.id.toString(),
    label: brand.name,
    disabled: !brand.is_active
  }));

  const unitOptions = units.map(unit => ({
    value: unit.id.toString(),
    label: unit.name,
    disabled: !unit.is_active
  }));

  const barcodeSymbologyOptions = [
    { value: 'CODE128', label: 'Code 128' },
    { value: 'UPC_A', label: 'UPC-A' },
    { value: 'EAN_13', label: 'EAN-13' },
    { value: 'EAN_8', label: 'EAN-8' },
    { value: 'CODE39', label: 'Code 39' },
    { value: 'CODE93', label: 'Code 93' },
    { value: 'CODABAR', label: 'Codabar' }
  ];

  return (
    <div className="space-y-6">

      {/* Product Name - Full width since warehouse is managed separately */}
      <div className="grid grid-cols-1 gap-6">
        <TextInput
          label="اسم المنتج "
          name="name"
          value={formData.name}
          onChange={(value) => updateFormData('name', value)}
          placeholder="أدخل اسم المنتج..."
          required
          error={errors.name}
          maxLength={100}
        />
      </div>

      {/* Slug and SKU */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <TextInput
          label="الرابط (Slug)"
          name="slug"
          value={formData.slug}
          onChange={(value) => updateFormData('slug', value)}
          placeholder="product-slug"
          dir="ltr"
          error={errors.slug}
        />

        <div className="relative">
          <TextInput
            label="الباركود الداخلي (SKU)"
            name="sku"
            value={formData.sku}
            onChange={(value) => updateFormData('sku', value)}
            placeholder="SKU123456"
            dir="ltr"
            error={errors.sku}
          />
          <button
            type="button"
            onClick={async () => {
              try {
                const result = await useProductStore.getState().generateBarcode('CODE128');
                updateFormData('sku', `SKU${result.barcode.slice(-8)}`);
              } catch (error) {
                console.error('خطأ في توليد SKU:', error);
                updateFormData('sku', generateSKU());
              }
            }}
            className="absolute left-2 top-9 p-2 rounded-lg bg-gray-100 dark:bg-gray-700 text-gray-500 hover:text-primary-600 dark:hover:text-primary-400 hover:bg-gray-200 dark:hover:bg-gray-600 transition-all duration-200 ease-in-out border-2 border-transparent hover:border-gray-300 dark:hover:border-gray-500 focus:outline-none focus:ring-4 focus:ring-primary-500/20"
            title="توليد SKU جديد"
          >
            <FaSync className="w-4 h-4" />
          </button>
        </div>
      </div>

      {/* Category and Subcategory */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <ModalSelectInput
          label="التصنيف"
          name="category_id"
          value={formData.category_id?.toString() || ''}
          onChange={(value) => {
            updateFormData('category_id', value ? parseInt(value) : null);
            updateFormData('subcategory_id', null); // Reset subcategory
          }}
          options={categoryOptions}
          placeholder="اختر التصنيف..."
          searchable
          error={errors.category_id}
        />

        <ModalSelectInput
          label="التصنيف الفرعي"
          name="subcategory_id"
          value={formData.subcategory_id?.toString() || ''}
          onChange={(value) => updateFormData('subcategory_id', value ? parseInt(value) : null)}
          options={subcategoryOptions}
          placeholder="اختر التصنيف الفرعي..."
          searchable
          disabled={!formData.category_id}
          error={errors.subcategory_id}
        />
      </div>

      {/* Brand and Unit */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <ModalSelectInput
          label="العلامة التجارية"
          name="brand_id"
          value={formData.brand_id?.toString() || ''}
          onChange={(value) => updateFormData('brand_id', value ? parseInt(value) : null)}
          options={brandOptions}
          placeholder="اختر العلامة التجارية..."
          searchable
          error={errors.brand_id}
        />

        <ModalSelectInput
          label="الوحدة"
          name="unit_id"
          value={formData.unit_id?.toString() || ''}
          onChange={(value) => updateFormData('unit_id', value ? parseInt(value) : null)}
          options={unitOptions}
          placeholder="اختر الوحدة..."
          searchable
          error={errors.unit_id}
        />
      </div>

      {/* Barcode Section */}
      <div className="bg-gray-50 dark:bg-gray-800/50 rounded-xl p-6 border border-gray-200 dark:border-gray-700">
        <div className="flex items-center gap-3 mb-4">
          <FiCode className="w-5 h-5 text-primary-600 dark:text-primary-400" />
          <h4 className="text-md font-medium text-gray-900 dark:text-white">
            إعدادات الباركود
          </h4>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <ModalSelectInput
            label="نوع الباركود"
            name="barcode_symbology"
            value={formData.barcode_symbology}
            onChange={(value) => updateFormData('barcode_symbology', value)}
            options={barcodeSymbologyOptions}
            placeholder="اختر نوع الباركود..."
            error={errors.barcode_symbology}
          />

          <div className="relative">
            <TextInput
              label="باركود العنصر"
              name="item_barcode"
              value={formData.item_barcode}
              onChange={(value) => updateFormData('item_barcode', value)}
              placeholder="123456789012"
              dir="ltr"
              error={errors.item_barcode}
            />
            <button
              type="button"
              onClick={async () => {
                try {
                  const result = await useProductStore.getState().generateBarcode(formData.barcode_symbology || 'CODE128');
                  updateFormData('item_barcode', result.barcode);
                } catch (error) {
                  console.error('خطأ في توليد الباركود:', error);
                  updateFormData('item_barcode', generateBarcode());
                }
              }}
              className="absolute left-2 top-9 p-2 text-gray-500 hover:text-primary-600 dark:hover:text-primary-400 transition-colors"
              title="توليد باركود جديد"
            >
              <FaSync className="w-4 h-4" />
            </button>
          </div>
        </div>
      </div>

      {/* Description - Rich Text Editor */}
      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          الوصف
        </label>
        <ArabicRichTextEditorWithButtons
          value={formData.description || ''}
          onChange={(value: string) => updateFormData('description', value)}
          placeholder="اكتب وصف المنتج هنا... يمكنك استخدام التنسيق المتقدم مثل العريض والمائل والقوائم"
          minHeight="200px"
        />
        {errors.description && (
          <p className="mt-2 text-sm text-red-600 dark:text-red-500">{errors.description}</p>
        )}
        <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">
          يمكنك استخدام التنسيق المتقدم للنص مثل <strong>العريض</strong> و <em>المائل</em> والقوائم النقطية
        </p>
      </div>

      {/* Loading indicator */}
      {(loadingData || brandsLoading || unitsLoading) && (
        <div className="flex items-center justify-center py-4">
          <div className="flex items-center gap-2 text-gray-500 dark:text-gray-400">
            <FaSync className="w-4 h-4 animate-spin" />
            <span className="text-sm">جاري تحميل البيانات...</span>
          </div>
        </div>
      )}
    </div>
  );
};

export default BasicInfoSection;
